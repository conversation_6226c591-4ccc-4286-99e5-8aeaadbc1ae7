'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface StreamingMarkdownProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  showCursor?: boolean
}

/**
 * 真正的流式Markdown渲染器
 * 在流式过程中实时渲染markdown，并在末尾显示光标
 */
export const StreamingMarkdown = memo(function StreamingMarkdown({
  content,
  className = '',
  isStreaming = false,
  onComplete,
  showCursor = true
}: StreamingMarkdownProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [cursorVisible, setCursorVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)

      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isStreaming && showCursor) {
      setCursorVisible(true)
      
      interval = setInterval(() => {
        setCursorVisible(prev => !prev)
      }, 500)
    } else {
      setCursorVisible(false)
      if (!isStreaming) {
        onComplete?.()
      }
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, showCursor, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // 自定义markdown组件
  const components = {
    // 自定义段落组件
    p: ({ children, ...props }: any) => (
      <p className="text-gray-700 mb-3 leading-relaxed" {...props}>
        {children}
      </p>
    ),
    
    // 自定义标题样式
    h1: ({ children }: any) => (
      <h1 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
        {children}
      </h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-lg font-semibold text-gray-900 mb-3 mt-6">
        {children}
      </h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-base font-medium text-gray-900 mb-2 mt-4">
        {children}
      </h3>
    ),
    h4: ({ children }: any) => (
      <h4 className="text-sm font-medium text-gray-900 mb-2 mt-3">
        {children}
      </h4>
    ),
    
    // 自定义列表样式
    ul: ({ children }: any) => (
      <ul className="list-disc list-inside mb-3 space-y-1 text-gray-700">
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside mb-3 space-y-1 text-gray-700">
        {children}
      </ol>
    ),
    li: ({ children }: any) => (
      <li className="text-gray-700">
        {children}
      </li>
    ),
    
    // 自定义代码样式
    code: ({ inline, children, ...props }: any) => {
      if (inline) {
        return (
          <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono" {...props}>
            {children}
          </code>
        )
      }
      return (
        <code className="block bg-gray-100 text-gray-800 p-3 rounded text-xs font-mono overflow-x-auto" {...props}>
          {children}
        </code>
      )
    },
    
    // 自定义代码块样式
    pre: ({ children }: any) => (
      <pre className="bg-gray-100 p-3 rounded mb-3 overflow-x-auto">
        {children}
      </pre>
    ),
    
    // 自定义引用样式
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-3 bg-blue-50 text-gray-700 italic">
        {children}
      </blockquote>
    ),
    
    // 自定义表格样式
    table: ({ children }: any) => (
      <div className="overflow-x-auto mb-3">
        <table className="min-w-full border border-gray-300 rounded">
          {children}
        </table>
      </div>
    ),
    thead: ({ children }: any) => (
      <thead className="bg-gray-50">
        {children}
      </thead>
    ),
    tbody: ({ children }: any) => (
      <tbody className="bg-white">
        {children}
      </tbody>
    ),
    tr: ({ children }: any) => (
      <tr className="border-b border-gray-200">
        {children}
      </tr>
    ),
    th: ({ children }: any) => (
      <th className="px-4 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider border-r border-gray-200 last:border-r-0">
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td className="px-4 py-2 text-sm text-gray-700 border-r border-gray-200 last:border-r-0">
        {children}
      </td>
    ),
    
    // 自定义链接样式
    a: ({ children, href }: any) => (
      <a 
        href={href} 
        className="text-blue-600 hover:text-blue-800 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
    
    // 自定义强调样式
    strong: ({ children }: any) => (
      <strong className="font-semibold text-gray-900">
        {children}
      </strong>
    ),
    em: ({ children }: any) => (
      <em className="italic text-gray-700">
        {children}
      </em>
    ),
    
    // 自定义分割线样式
    hr: () => (
      <hr className="my-6 border-gray-300" />
    ),
  }

  // Markdown渲染模式
  return (
    <div className={className}>
      <div
        ref={containerRef}
        className="bg-white rounded-lg border border-gray-200 overflow-hidden"
        style={{
          maxHeight: '600px',
          overflowY: 'auto'
        }}
      >
        <div className="p-6 prose prose-sm max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight]}
            components={components}
          >
            {displayContent}
          </ReactMarkdown>
          {/* 流式状态下在末尾添加光标 */}
          {isStreaming && cursorVisible && (
            <span className="inline-block w-0.5 h-5 bg-blue-500 ml-1 animate-pulse" />
          )}
        </div>
      </div>

      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>AI正在分析中，请稍候...</span>
          </div>
        </div>
      )}
    </div>
  )
})

export default StreamingMarkdown
