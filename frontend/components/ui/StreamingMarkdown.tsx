'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface StreamingMarkdownProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  showCursor?: boolean
}

/**
 * 真正的流式Markdown渲染器
 * 在流式过程中实时渲染markdown，并在末尾显示光标
 */
const StreamingMarkdown = memo(function StreamingMarkdown({
  content,
  className = '',
  isStreaming = false,
  onComplete,
  showCursor = true
}: StreamingMarkdownProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [cursorVisible, setCursorVisible] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)

      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isStreaming && showCursor) {
      setCursorVisible(true)
      
      interval = setInterval(() => {
        setCursorVisible(prev => !prev)
      }, 500)
    } else {
      setCursorVisible(false)
      if (!isStreaming) {
        onComplete?.()
      }
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, showCursor, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])



  // Markdown渲染模式
  return (
    <div className={className}>
      <div
        ref={containerRef}
        className="bg-white rounded-lg border border-gray-200 overflow-hidden"
        style={{
          maxHeight: '600px',
          overflowY: 'auto'
        }}
      >
        <div className="p-6 relative">
          <MarkdownRenderer content={displayContent} />
          {/* 流式状态下在末尾添加光标 */}
          {isStreaming && cursorVisible && (
            <span className="inline-block w-0.5 h-5 bg-blue-500 ml-1 animate-pulse" />
          )}
        </div>
      </div>

      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>AI正在分析中，请稍候...</span>
          </div>
        </div>
      )}
    </div>
  )
})

export default StreamingMarkdown
