'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface SimpleStreamingMarkdownProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  enableStreamingMarkdown?: boolean // 新增：是否启用流式markdown渲染
}

/**
 * 简化的流式Markdown渲染器
 * 支持真正的流式markdown渲染，在流式过程中实时显示markdown格式
 */
export const SimpleStreamingMarkdown = memo(function SimpleStreamingMarkdown({
  content,
  className = '',
  isStreaming = false,
  onComplete,
  enableStreamingMarkdown = true // 默认启用流式markdown渲染
}: SimpleStreamingMarkdownProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [showCursor, setShowCursor] = useState(false)
  const [renderMode, setRenderMode] = useState<'text' | 'markdown'>('markdown') // 默认使用markdown模式
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)

      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isStreaming) {
      setShowCursor(true)

      interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
    } else {
      setShowCursor(false)
      onComplete?.()
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, onComplete])

  // 初始化渲染模式
  useEffect(() => {
    if (enableStreamingMarkdown) {
      setRenderMode('markdown')
    } else {
      setRenderMode('text')
    }
  }, [enableStreamingMarkdown])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Markdown渲染模式（支持流式状态）
  if (renderMode === 'markdown') {
    return (
      <div className={className}>
        <div
          ref={containerRef}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden"
          style={{
            maxHeight: '600px',
            overflowY: 'auto'
          }}
        >
          <div className="p-6 relative">
            <MarkdownRenderer
              content={displayContent}
              className=""
            />
            {/* 流式状态下的光标 */}
            {isStreaming && (
              <span
                className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
                  showCursor ? 'opacity-100' : 'opacity-0'
                }`}
                style={{ verticalAlign: 'text-top' }}
              />
            )}
          </div>
        </div>

        {/* 流式状态指示器 */}
        {isStreaming && (
          <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>AI正在分析中，请稍候...</span>
            </div>
          </div>
        )}

        {/* 渲染模式切换按钮 */}
        <div className="mt-4 flex justify-center space-x-3">
          <button
            onClick={() => setRenderMode('text')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'text'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            原始文本
          </button>
          <button
            onClick={() => setRenderMode('markdown')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'markdown'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Markdown渲染
          </button>
        </div>
      </div>
    )
  }

  // 文本模式（手动选择）
  return (
    <div className={className}>
      <div
        ref={containerRef}
        className="bg-gray-50 border border-gray-200 rounded-lg p-4 font-mono text-sm"
        style={{
          maxHeight: '600px',
          overflowY: 'auto',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}
      >
        {displayContent}
        {/* 流式输入光标 */}
        {isStreaming && (
          <span
            className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
              showCursor ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ verticalAlign: 'text-top' }}
          />
        )}
      </div>

      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>AI正在分析中，请稍候...</span>
          </div>
        </div>
      )}

      {/* 渲染模式切换按钮 */}
      <div className="mt-4 flex justify-center space-x-3">
        <button
          onClick={() => setRenderMode('text')}
          className={`px-4 py-2 text-sm rounded-lg transition-colors ${
            renderMode === 'text'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          原始文本
        </button>
        <button
          onClick={() => setRenderMode('markdown')}
          className={`px-4 py-2 text-sm rounded-lg transition-colors ${
            renderMode === 'markdown'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Markdown渲染
        </button>
      </div>
    </div>
  )
})

export default SimpleStreamingMarkdown
